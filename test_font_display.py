"""
测试中文字体显示
验证matplotlib中文字体配置是否正常工作
"""

import matplotlib.pyplot as plt
import matplotlib
import platform
import numpy as np

def setup_chinese_font():
    """配置matplotlib中文字体"""
    system = platform.system()
    if system == "Windows":
        # Windows系统常用中文字体
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Heiti TC', 'STHeiti']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'SimHei']
    
    for font in fonts:
        try:
            matplotlib.rcParams['font.sans-serif'] = [font]
            matplotlib.rcParams['axes.unicode_minus'] = False
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"Successfully configured font: {font}")
            return font
        except Exception as e:
            print(f"Failed to use font {font}: {e}")
            continue
    
    print("Warning: No suitable Chinese font found, Chinese text may display as squares")
    # 使用默认字体
    matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
    matplotlib.rcParams['axes.unicode_minus'] = False
    return "DejaVu Sans"

def test_chinese_display():
    """测试中文显示效果"""
    # 配置字体
    font_used = setup_chinese_font()
    
    # 创建测试图表
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 测试1: 简单文本
    axes[0, 0].text(0.5, 0.5, '中文测试\nChinese Test', 
                    fontsize=14, ha='center', va='center')
    axes[0, 0].set_title('Text Display Test')
    axes[0, 0].set_xlim(0, 1)
    axes[0, 0].set_ylim(0, 1)
    
    # 测试2: 柱状图
    categories = ['设备A', '设备B', '设备C', '设备D']
    values = [23, 45, 56, 78]
    axes[0, 1].bar(categories, values)
    axes[0, 1].set_title('Bar Chart with Chinese Labels')
    axes[0, 1].set_ylabel('数值 (Value)')
    plt.setp(axes[0, 1].xaxis.get_majorticklabels(), rotation=45)
    
    # 测试3: 线图
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    axes[1, 0].plot(x, y, label='正弦波 (Sine Wave)')
    axes[1, 0].set_xlabel('时间 (Time)')
    axes[1, 0].set_ylabel('幅度 (Amplitude)')
    axes[1, 0].set_title('Line Plot with Chinese Labels')
    axes[1, 0].legend()
    axes[1, 0].grid(True)
    
    # 测试4: 散点图
    x_scatter = np.random.randn(50)
    y_scatter = np.random.randn(50)
    colors = np.random.rand(50)
    axes[1, 1].scatter(x_scatter, y_scatter, c=colors, alpha=0.6)
    axes[1, 1].set_xlabel('X轴 (X-axis)')
    axes[1, 1].set_ylabel('Y轴 (Y-axis)')
    axes[1, 1].set_title('Scatter Plot with Chinese Labels')
    
    plt.tight_layout()
    
    # 添加总标题
    fig.suptitle(f'Chinese Font Display Test (Using: {font_used})', 
                 fontsize=16, y=0.98)
    
    plt.show()
    
    print("\nFont test completed!")
    print("If you see squares (□) instead of Chinese characters, ")
    print("please install a Chinese font on your system.")
    print("\nFor Windows: SimHei, Microsoft YaHei, or SimSun")
    print("For macOS: Arial Unicode MS or Heiti TC")
    print("For Linux: WenQuanYi Micro Hei or install Chinese fonts")

def test_available_fonts():
    """列出系统可用的字体"""
    from matplotlib import font_manager
    
    print("Available fonts on your system:")
    fonts = font_manager.findSystemFonts()
    font_names = []
    
    for font_path in fonts:
        try:
            font_prop = font_manager.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            if font_name not in font_names:
                font_names.append(font_name)
        except:
            continue
    
    # 过滤可能支持中文的字体
    chinese_fonts = []
    chinese_keywords = ['SimHei', 'SimSun', 'Microsoft', 'YaHei', 'KaiTi', 
                       'Heiti', 'STHeiti', 'WenQuanYi', 'Noto', 'Source Han']
    
    for font_name in sorted(font_names):
        for keyword in chinese_keywords:
            if keyword.lower() in font_name.lower():
                chinese_fonts.append(font_name)
                break
    
    print(f"\nTotal fonts found: {len(font_names)}")
    print(f"Potential Chinese fonts: {len(chinese_fonts)}")
    
    if chinese_fonts:
        print("\nPotential Chinese fonts:")
        for font in chinese_fonts[:10]:  # 显示前10个
            print(f"  - {font}")
        if len(chinese_fonts) > 10:
            print(f"  ... and {len(chinese_fonts) - 10} more")
    else:
        print("\nNo obvious Chinese fonts found.")
        print("You may need to install Chinese fonts manually.")

if __name__ == "__main__":
    print("=== Chinese Font Display Test ===")
    print(f"Operating System: {platform.system()}")
    print(f"Platform: {platform.platform()}")
    
    # 测试可用字体
    test_available_fonts()
    
    print("\n" + "="*50)
    
    # 测试中文显示
    test_chinese_display()
