# 缺失片数预测使用指南

## 功能概述

这个功能专门用于预测特定条件组合（equip, sub_equip, capability, recipe）下缺失片数的RT值，并生成包含实际值和预测值的完整数据集。

## 主要特性

1. **完整覆盖**：为1-25的所有片数生成RT值
2. **结果标识**：清楚标识哪些是实际值（actual），哪些是预测值（predicted）
3. **数据导出**：自动导出CSV文件，包含result_type列
4. **灵活使用**：支持单个条件组合或批量处理

## 使用方法

### 方法1：使用专门的预测脚本

```python
# 运行专门的预测脚本
python predict_missing_qty.py
```

### 方法2：在代码中调用

```python
from predict_missing_qty import custom_prediction

# 定义现有数据
existing_data = {
    1: 8.5, 2: 9.2, 3: 10.1, 4: 11.3, 6: 13.8, 8: 16.2, 9: 17.1, 10: 18.5,
    15: 25.8, 16: 27.2, 17: 28.6, 18: 30.1, 19: 31.5, 20: 33.2, 25: 42.1
}

# 进行预测
result_df = custom_prediction(
    equip='EQ001',
    sub_equip='SUB_A', 
    capability='CAP_HIGH',
    recipe='RECIPE_1',
    existing_data_dict=existing_data
)
```

### 方法3：使用example_usage.py中的函数

```python
from example_usage import predict_specific_condition_missing_qty

# 分别提供qty和rt列表
existing_qtys = [1, 2, 3, 4, 6, 8, 9, 10, 15, 16, 17, 18, 19, 20, 25]
existing_rts = [8.5, 9.2, 10.1, 11.3, 13.8, 16.2, 17.1, 18.5, 25.8, 27.2, 28.6, 30.1, 31.5, 33.2, 42.1]

result_df = predict_specific_condition_missing_qty(
    equip='EQ001',
    sub_equip='SUB_A',
    capability='CAP_HIGH', 
    recipe='RECIPE_1',
    existing_qtys=existing_qtys,
    existing_rts=existing_rts,
    predictor=predictor
)
```

## 输出结果

### 控制台输出示例

```
============================================================
Predicting Missing Qty for Condition:
Equipment: EQ001
Sub-Equipment: SUB_A
Capability: CAP_HIGH
Recipe: RECIPE_1
============================================================

Existing qty values: [1, 2, 3, 4, 6, 8, 9, 10, 15, 16, 17, 18, 19, 20, 25]
Missing qty values: [5, 7, 11, 12, 13, 14, 21, 22, 23, 24]
Total missing: 10 out of 25

Predicting RT values for missing qty: [5, 7, 11, 12, 13, 14, 21, 22, 23, 24]
✓ Successfully predicted 10 values

============================================================
COMPLETE RESULTS
============================================================
Qty   RT           Type         Status
----- ------------ ------------ --------------------
1     8.50         actual       ✓ Actual
2     9.20         actual       ✓ Actual
3     10.10        actual       ✓ Actual
4     11.30        actual       ✓ Actual
5     12.45        predicted    🔮 Predicted
6     13.80        actual       ✓ Actual
7     15.23        predicted    🔮 Predicted
...
```

### CSV文件输出

生成的CSV文件包含以下列：

| equip | sub_equip | capability | recipe   | qty | rt    | result_type |
|-------|-----------|------------|----------|-----|-------|-------------|
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 1   | 8.50  | actual      |
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 2   | 9.20  | actual      |
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 3   | 10.10 | actual      |
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 4   | 11.30 | actual      |
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 5   | 12.45 | predicted   |
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 6   | 13.80 | actual      |
| EQ001 | SUB_A     | CAP_HIGH   | RECIPE_1 | 7   | 15.23 | predicted   |
| ...   | ...       | ...        | ...      | ... | ...   | ...         |

## 实际使用步骤

### 步骤1：训练模型
```bash
python example_usage.py
```

### 步骤2：准备您的数据
根据您的实际情况，修改现有数据：

```python
# 您的实际数据
your_existing_data = {
    1: 实际RT值1,
    2: 实际RT值2,
    3: 实际RT值3,
    4: 实际RT值4,
    6: 实际RT值6,
    8: 实际RT值8,
    9: 实际RT值9,
    10: 实际RT值10,
    15: 实际RT值15,
    16: 实际RT值16,
    17: 实际RT值17,
    18: 实际RT值18,
    19: 实际RT值19,
    20: 实际RT值20,
    25: 实际RT值25
}
```

### 步骤3：运行预测
```python
from predict_missing_qty import custom_prediction

result = custom_prediction(
    equip='您的设备名',
    sub_equip='您的子设备名',
    capability='您的能力等级',
    recipe='您的菜单名',
    existing_data_dict=your_existing_data
)
```

### 步骤4：查看结果
- 检查控制台输出的详细信息
- 查看生成的CSV文件
- result_type列标识实际值和预测值

## 批量处理多个条件组合

如果您有多个条件组合需要处理：

```python
from example_usage import predict_missing_qty_for_conditions

# 定义多个条件组合
conditions = [
    {'equip': 'EQ001', 'sub_equip': 'SUB_A', 'capability': 'CAP_HIGH', 'recipe': 'RECIPE_1'},
    {'equip': 'EQ002', 'sub_equip': 'SUB_B', 'capability': 'CAP_MED', 'recipe': 'RECIPE_2'},
    # 添加更多条件组合...
]

# 批量处理
result_df = predict_missing_qty_for_conditions(
    df=your_training_data,
    predictor=predictor,
    target_conditions=conditions,
    export_path="batch_predictions.csv"
)
```

## 注意事项

1. **模型训练**：确保先运行`example_usage.py`训练模型
2. **数据格式**：现有数据必须是字典格式 `{qty: rt}`
3. **条件组合**：确保条件组合在训练数据中存在或相似
4. **文件输出**：CSV文件会自动保存到当前目录
5. **结果验证**：检查预测值的合理性，必要时调整模型参数

## 文件说明

- `predict_missing_qty.py` - 专门的预测脚本
- `example_usage.py` - 包含多种预测函数的主脚本
- `rt_prediction_model.py` - 核心预测模型
- 生成的CSV文件 - 包含完整预测结果

这个解决方案完全满足您的需求：为特定条件组合预测缺失片数的RT值，并清楚标识实际值和预测值。
